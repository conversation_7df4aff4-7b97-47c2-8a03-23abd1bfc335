/**
 * Test script to verify blog post creation works with the constraint fix
 */

const testBlogCreation = async () => {
  try {
    console.log('🧪 Testing blog post creation...');
    
    const testPost = {
      title: 'Test Post - ' + new Date().toISOString(),
      content: 'This is a test post to verify the database constraint fix.',
      excerpt: 'Test post excerpt',
      status: 'published',
      author_name: 'Test Author'
    };

    console.log('📝 Creating test post:', testPost.title);

    const response = await fetch('http://localhost:3000/api/blog/posts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPost)
    });

    const result = await response.json();

    if (result.success) {
      console.log('✅ Test passed! Post created successfully:');
      console.log('   ID:', result.post.id);
      console.log('   Title:', result.post.title);
      console.log('   Status:', result.post.status);
      console.log('   Created at:', result.post.created_at);
      console.log('   Published at:', result.post.published_at);
      
      // Verify the constraint is satisfied
      const createdAt = new Date(result.post.created_at);
      const publishedAt = new Date(result.post.published_at);
      
      if (publishedAt >= createdAt) {
        console.log('✅ Constraint satisfied: published_at >= created_at');
      } else {
        console.log('❌ Constraint violation: published_at < created_at');
      }
    } else {
      console.log('❌ Test failed:', result.message);
    }

  } catch (error) {
    console.error('❌ Test error:', error.message);
  }
};

// Run the test if this script is executed directly
if (require.main === module) {
  testBlogCreation();
}

module.exports = { testBlogCreation };
